<header class="bg-secondary-900 text-white shadow-strong sticky top-0 z-40">
  <!-- Top announcement bar with animation -->
  <div class="bg-accent-600 py-1.5 text-white text-center text-sm relative overflow-hidden">
    <div class="animate-pulse-slow absolute inset-0 bg-accent-500 opacity-30"></div>
    <div class="container mx-auto px-4 relative">
      <div class="flex items-center justify-center">
        <i class="fas fa-bolt mr-2"></i>
        <span class="font-medium">New feature: Live match notifications now available!</span>
        <a href="#" class="ml-2 underline hover:text-white/90 font-semibold">Learn more</a>
      </div>
    </div>
  </div>

  <div class="container mx-auto px-4">
    <!-- Top navigation bar -->
    <div class="flex justify-between items-center py-4">
      <a href="/" class="text-xl sm:text-2xl font-bold flex items-center group" data-aos="fade-right" data-aos-delay="100">
        <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center mr-3 shadow-glow transform transition-all duration-300 group-hover:rotate-6 group-hover:scale-110">
          <i class="fas fa-basketball-ball text-white text-xl"></i>
        </div>
        <div class="flex flex-col">
          <span class="hidden xs:inline font-heading tracking-wide text-white">SportZone</span>
          <span class="xs:hidden font-heading tracking-wide text-white">SZ</span>
          <span class="text-xs text-primary-300 hidden sm:inline-block -mt-1">Live Sports & News</span>
        </div>
      </a>

      <!-- Search bar - Desktop -->
      <div class="hidden lg:flex items-center mx-4 flex-1 max-w-md" data-aos="fade-down" data-aos-delay="200">
        <div class="relative w-full">
          <input type="text" placeholder="Search for news, teams, matches..."
                 class="w-full bg-secondary-800/50 border border-secondary-700 rounded-lg py-2 pl-10 pr-4 text-white placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300">
          <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400"></i>
        </div>
      </div>

      <!-- Auth buttons - Desktop -->
      <div class="hidden md:flex space-x-3 items-center" data-aos="fade-left" data-aos-delay="300">
        <% if (locals.user) { %>
          <div class="relative group">
            <button class="flex items-center space-x-2 bg-secondary-800 hover:bg-secondary-700 rounded-lg py-2 px-3 transition-all duration-300">
              <div class="w-8 h-8 rounded-full bg-primary-600 flex items-center justify-center text-white">
                <span class="font-semibold"><%= user.username.charAt(0).toUpperCase() %></span>
              </div>
              <span class="hidden lg:inline font-medium"><%= user.username %></span>
              <i class="fas fa-chevron-down text-xs opacity-70"></i>
            </button>

            <!-- Dropdown menu -->
            <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-strong border border-secondary-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform origin-top-right group-hover:translate-y-0 translate-y-2 z-50">
              <div class="py-2">
                <a href="/auth/profile" class="flex items-center px-4 py-2 text-secondary-700 hover:bg-secondary-50 hover:text-primary-600 transition-colors duration-200">
                  <i class="fas fa-user-circle mr-2"></i> My Profile
                </a>
                <a href="#" class="flex items-center px-4 py-2 text-secondary-700 hover:bg-secondary-50 hover:text-primary-600 transition-colors duration-200">
                  <i class="fas fa-cog mr-2"></i> Settings
                </a>
                <a href="#" class="flex items-center px-4 py-2 text-secondary-700 hover:bg-secondary-50 hover:text-primary-600 transition-colors duration-200">
                  <i class="fas fa-bell mr-2"></i> Notifications
                </a>
                <% if (user.username.includes('admin')) { %>
                  <div class="border-t border-secondary-200 my-1"></div>
                  <a href="/fantasy/admin/payments" class="flex items-center px-4 py-2 text-orange-600 hover:bg-orange-50 hover:text-orange-700 transition-colors duration-200">
                    <i class="fas fa-shield-alt mr-2"></i> Admin Panel
                  </a>
                <% } %>
                <div class="border-t border-secondary-200 my-1"></div>
                <a href="/auth/logout" class="flex items-center px-4 py-2 text-red-600 hover:bg-red-50 transition-colors duration-200">
                  <i class="fas fa-sign-out-alt mr-2"></i> Logout
                </a>
              </div>
            </div>
          </div>
        <% } else { %>
          <a href="/auth/login" class="btn-ghost text-white hover:text-primary-300 py-2 px-4 rounded-lg transition-all duration-300">
            <i class="fas fa-sign-in-alt mr-1.5"></i> Login
          </a>
          <a href="/auth/register" class="btn-primary py-2 px-4 rounded-lg shadow-glow transition-all duration-300 transform hover:translate-y-[-2px]">
            <i class="fas fa-user-plus mr-1.5"></i> Register
          </a>
        <% } %>
      </div>

      <!-- Mobile menu button -->
      <button id="mobile-menu-button" class="md:hidden text-white focus:outline-none p-2 rounded-lg hover:bg-secondary-800 transition-all duration-300" aria-label="Toggle menu">
        <div class="w-6 h-5 flex flex-col justify-between">
          <span class="w-full h-0.5 bg-white rounded-full transition-all duration-300" id="menu-bar-1"></span>
          <span class="w-full h-0.5 bg-white rounded-full transition-all duration-300" id="menu-bar-2"></span>
          <span class="w-full h-0.5 bg-white rounded-full transition-all duration-300" id="menu-bar-3"></span>
        </div>
      </button>
    </div>

    <!-- Main navigation - Desktop -->
    <nav class="hidden md:block py-3 border-t border-secondary-800" data-aos="fade-up" data-aos-delay="400">
      <ul class="flex flex-wrap justify-between">
        <li class="group">
          <a href="/" class="flex items-center py-2 px-3 text-white/90 hover:text-white transition-all duration-300">
            <i class="fas fa-home mr-2 text-primary-400 group-hover:text-primary-300 transition-colors duration-300"></i>
            <span>Home</span>
            <span class="block max-w-0 group-hover:max-w-full transition-all duration-500 h-0.5 bg-primary-500 mt-0.5"></span>
          </a>
        </li>
        <li class="group">
          <a href="/news" class="flex items-center py-2 px-3 text-white/90 hover:text-white transition-all duration-300">
            <i class="fas fa-newspaper mr-2 text-primary-400 group-hover:text-primary-300 transition-colors duration-300"></i>
            <span>News</span>
            <span class="block max-w-0 group-hover:max-w-full transition-all duration-500 h-0.5 bg-primary-500 mt-0.5"></span>
          </a>
        </li>
        <li class="group">
          <a href="/streaming" class="flex items-center py-2 px-3 text-white/90 hover:text-white transition-all duration-300">
            <i class="fas fa-play-circle mr-2 text-primary-400 group-hover:text-primary-300 transition-colors duration-300"></i>
            <span>Live Streaming</span>
            <span class="block max-w-0 group-hover:max-w-full transition-all duration-500 h-0.5 bg-primary-500 mt-0.5"></span>
          </a>
        </li>
        <li class="group">
          <a href="/fantasy" class="flex items-center py-2 px-3 text-white/90 hover:text-white transition-all duration-300">
            <i class="fas fa-trophy mr-2 text-primary-400 group-hover:text-primary-300 transition-colors duration-300"></i>
            <span>Fantasy Leagues</span>
            <span class="block max-w-0 group-hover:max-w-full transition-all duration-500 h-0.5 bg-primary-500 mt-0.5"></span>
          </a>
        </li>
        <li class="group">
          <a href="/about" class="flex items-center py-2 px-3 text-white/90 hover:text-white transition-all duration-300">
            <i class="fas fa-info-circle mr-2 text-primary-400 group-hover:text-primary-300 transition-colors duration-300"></i>
            <span>About</span>
            <span class="block max-w-0 group-hover:max-w-full transition-all duration-500 h-0.5 bg-primary-500 mt-0.5"></span>
          </a>
        </li>
      </ul>
    </nav>
  </div>

  <!-- Mobile menu (hidden by default) -->
  <div id="mobile-menu" class="hidden md:hidden bg-secondary-900 overflow-hidden transition-all duration-300 ease-in-out" style="max-height: 0;">
    <div class="container mx-auto px-4">
      <!-- Mobile search -->
      <div class="py-4 border-t border-secondary-800">
        <div class="relative">
          <input type="text" placeholder="Search..."
                 class="w-full bg-secondary-800 border border-secondary-700 rounded-lg py-2.5 pl-10 pr-4 text-white placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
          <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400"></i>
        </div>
      </div>

      <!-- Mobile navigation links -->
      <div class="py-2 border-t border-secondary-800">
        <ul class="space-y-1">
          <li>
            <a href="/" class="flex items-center py-3 px-4 rounded-lg hover:bg-secondary-800 transition-all duration-200">
              <div class="w-8 h-8 rounded-full bg-primary-600/20 flex items-center justify-center mr-3">
                <i class="fas fa-home text-primary-400"></i>
              </div>
              <span>Home</span>
            </a>
          </li>
          <li>
            <a href="/news" class="flex items-center py-3 px-4 rounded-lg hover:bg-secondary-800 transition-all duration-200">
              <div class="w-8 h-8 rounded-full bg-primary-600/20 flex items-center justify-center mr-3">
                <i class="fas fa-newspaper text-primary-400"></i>
              </div>
              <span>News</span>
            </a>
          </li>
          <li>
            <a href="/streaming" class="flex items-center py-3 px-4 rounded-lg hover:bg-secondary-800 transition-all duration-200">
              <div class="w-8 h-8 rounded-full bg-primary-600/20 flex items-center justify-center mr-3">
                <i class="fas fa-play-circle text-primary-400"></i>
              </div>
              <span>Live Streaming</span>
            </a>
          </li>
          <li>
            <a href="/fantasy" class="flex items-center py-3 px-4 rounded-lg hover:bg-secondary-800 transition-all duration-200">
              <div class="w-8 h-8 rounded-full bg-primary-600/20 flex items-center justify-center mr-3">
                <i class="fas fa-trophy text-primary-400"></i>
              </div>
              <span>Fantasy Leagues</span>
            </a>
          </li>
          <li>
            <a href="/about" class="flex items-center py-3 px-4 rounded-lg hover:bg-secondary-800 transition-all duration-200">
              <div class="w-8 h-8 rounded-full bg-primary-600/20 flex items-center justify-center mr-3">
                <i class="fas fa-info-circle text-primary-400"></i>
              </div>
              <span>About</span>
            </a>
          </li>
        </ul>
      </div>

      <!-- Mobile auth links -->
      <div class="py-4 border-t border-secondary-800">
        <% if (locals.user) { %>
          <div class="flex items-center px-4 py-2">
            <div class="w-10 h-10 rounded-full bg-primary-600 flex items-center justify-center text-white mr-3">
              <span class="font-semibold"><%= user.username.charAt(0).toUpperCase() %></span>
            </div>
            <div>
              <p class="font-medium"><%= user.username %></p>
              <p class="text-sm text-secondary-400">Member since <%= new Date(user.created_at || Date.now()).toLocaleDateString() %></p>
            </div>
          </div>
          <div class="mt-3 space-y-1 px-2">
            <a href="/auth/profile" class="flex items-center py-2 px-4 rounded-lg hover:bg-secondary-800 transition-all duration-200">
              <i class="fas fa-user-circle mr-3 text-primary-400"></i> My Profile
            </a>
            <a href="#" class="flex items-center py-2 px-4 rounded-lg hover:bg-secondary-800 transition-all duration-200">
              <i class="fas fa-cog mr-3 text-primary-400"></i> Settings
            </a>
            <a href="#" class="flex items-center py-2 px-4 rounded-lg hover:bg-secondary-800 transition-all duration-200">
              <i class="fas fa-bell mr-3 text-primary-400"></i> Notifications
            </a>
            <% if (user.username.includes('admin')) { %>
              <a href="/fantasy/admin/payments" class="flex items-center py-2 px-4 rounded-lg hover:bg-orange-900/30 text-orange-400 transition-all duration-200">
                <i class="fas fa-shield-alt mr-3"></i> Admin Panel
              </a>
            <% } %>
            <a href="/auth/logout" class="flex items-center py-2 px-4 rounded-lg hover:bg-red-900/30 text-red-400 transition-all duration-200 mt-2">
              <i class="fas fa-sign-out-alt mr-3"></i> Logout
            </a>
          </div>
        <% } else { %>
          <div class="flex flex-col space-y-2 px-2">
            <a href="/auth/login" class="btn-ghost text-white hover:bg-secondary-800 py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center">
              <i class="fas fa-sign-in-alt mr-2"></i> Login
            </a>
            <a href="/auth/register" class="btn-primary py-2.5 px-4 rounded-lg shadow-sm transition-all duration-200 flex items-center justify-center">
              <i class="fas fa-user-plus mr-2"></i> Register
            </a>
          </div>
        <% } %>
      </div>
    </div>
  </div>
</header>

<script>
  // Mobile menu toggle with animation
  document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const bar1 = document.getElementById('menu-bar-1');
    const bar2 = document.getElementById('menu-bar-2');
    const bar3 = document.getElementById('menu-bar-3');
    let isOpen = false;

    mobileMenuButton.addEventListener('click', function() {
      isOpen = !isOpen;

      // Animate hamburger to X
      if (isOpen) {
        bar1.classList.add('rotate-45', 'translate-y-[9px]');
        bar2.classList.add('opacity-0');
        bar3.classList.add('-rotate-45', '-translate-y-[9px]');

        // Show menu with animation
        mobileMenu.classList.remove('hidden');
        setTimeout(() => {
          mobileMenu.style.maxHeight = '1000px';
        }, 10);
      } else {
        bar1.classList.remove('rotate-45', 'translate-y-[9px]');
        bar2.classList.remove('opacity-0');
        bar3.classList.remove('-rotate-45', '-translate-y-[9px]');

        // Hide menu with animation
        mobileMenu.style.maxHeight = '0';
        setTimeout(() => {
          mobileMenu.classList.add('hidden');
        }, 300);
      }
    });

    // Close mobile menu on window resize if screen becomes larger
    window.addEventListener('resize', function() {
      if (window.innerWidth >= 768 && isOpen) {
        isOpen = false;
        bar1.classList.remove('rotate-45', 'translate-y-[9px]');
        bar2.classList.remove('opacity-0');
        bar3.classList.remove('-rotate-45', '-translate-y-[9px]');
        mobileMenu.style.maxHeight = '0';
        setTimeout(() => {
          mobileMenu.classList.add('hidden');
        }, 300);
      }
    });
  });
</script>

<style>
  /* Add xs breakpoint for very small screens */
  @media (min-width: 475px) {
    .xs\:inline {
      display: inline;
    }
    .xs\:hidden {
      display: none;
    }
  }
</style>
