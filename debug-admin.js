const db = require('./config/database');

async function debugAdmin() {
  try {
    console.log('🔍 Debugging admin setup...');

    // Check if admin user exists
    const [adminUsers] = await db.query('SELECT username, email FROM users WHERE username LIKE "%admin%"');
    console.log('Admin users found:', adminUsers);

    // Check pending payments
    const [pendingPayments] = await db.query('SELECT COUNT(*) as count FROM payment_transactions WHERE payment_status = "pending"');
    console.log('Pending payments:', pendingPayments[0].count);

    // Check if we can create a simple payment
    const txnRef = `DEBUG_${Date.now()}`;
    console.log('Creating test payment:', txnRef);

    await db.query(`
      INSERT INTO payment_transactions (user_id, league_id, amount, payment_method, transaction_reference, payment_proof, payment_status)
      VALUES (2, 1, 25.00, 'bank_transfer', ?, 'Debug test payment', 'pending')
    `, [txnRef]);

    console.log('✅ Test payment created successfully!');
    console.log('');
    console.log('🎯 ADMIN LOGIN INSTRUCTIONS:');
    console.log('1. Go to: http://192.168.45.125:3000/auth/login');
    console.log('2. Username: admin');
    console.log('3. Password: admin123');
    console.log('4. After login, go to: http://192.168.45.125:3000/fantasy/admin/payments');
    console.log('');
    console.log('If you still get 404, the issue might be:');
    console.log('- Not logged in as admin');
    console.log('- Session expired');
    console.log('- Browser cache issue');

    process.exit(0);
  } catch (error) {
    console.error('❌ Debug error:', error.message);
    process.exit(1);
  }
}

debugAdmin();
