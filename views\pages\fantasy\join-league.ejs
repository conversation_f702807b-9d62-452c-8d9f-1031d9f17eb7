<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
  <div class="max-w-4xl mx-auto px-4">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-gray-900 mb-2">Join Fantasy League</h1>
      <p class="text-lg text-gray-600">Complete payment to start building your dream team</p>
    </div>

    <!-- League Info Card -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h2 class="text-2xl font-bold text-gray-900"><%= league.name %></h2>
          <p class="text-gray-600"><%= league.category_name %> League</p>
        </div>
        <div class="text-right">
          <div class="text-3xl font-bold text-green-600">$<%= league.entry_fee %></div>
          <div class="text-sm text-gray-500">Entry Fee</div>
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div class="bg-blue-50 p-4 rounded-lg">
          <div class="text-2xl font-bold text-blue-600">$<%= league.prize_pool || (league.entry_fee * 20) %></div>
          <div class="text-sm text-gray-600">Prize Pool</div>
        </div>
        <div class="bg-green-50 p-4 rounded-lg">
          <div class="text-2xl font-bold text-green-600"><%= new Date(league.start_date).toLocaleDateString() %></div>
          <div class="text-sm text-gray-600">Start Date</div>
        </div>
        <div class="bg-purple-50 p-4 rounded-lg">
          <div class="text-2xl font-bold text-purple-600">11</div>
          <div class="text-sm text-gray-600">Players per Team</div>
        </div>
      </div>
      
      <p class="text-gray-700"><%= league.description || 'Build your fantasy team and compete for prizes!' %></p>
    </div>

    <!-- Payment Form -->
    <div class="bg-white rounded-xl shadow-lg p-6">
      <h3 class="text-xl font-bold text-gray-900 mb-6">💳 Payment Information</h3>
      
      <form id="paymentForm" class="space-y-6">
        <!-- Payment Method Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-3">Select Payment Method</label>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <% paymentMethods.forEach(method => { %>
              <div class="payment-method-card border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors" data-method="<%= method.method_type %>">
                <div class="flex items-center">
                  <input type="radio" name="paymentMethod" value="<%= method.method_type %>" id="method_<%= method.id %>" class="mr-3">
                  <div>
                    <h4 class="font-semibold text-gray-900"><%= method.method_name %></h4>
                    <p class="text-sm text-gray-600"><%= method.instructions %></p>
                  </div>
                </div>
                
                <!-- Payment Details -->
                <div class="payment-details mt-4 hidden">
                  <% if (method.method_type === 'bank_transfer') { %>
                    <div class="bg-gray-50 p-3 rounded text-sm">
                      <%
                        let details;
                        try {
                          // Check if account_details is already an object or needs parsing
                          details = typeof method.account_details === 'string' ?
                                   JSON.parse(method.account_details) :
                                   method.account_details;
                        } catch (e) {
                          details = {}; // Fallback to empty object if parsing fails
                        }
                      %>
                      <p><strong>Bank:</strong> <%= details.bank_name || 'N/A' %></p>
                      <p><strong>Account:</strong> <%= details.account_number || 'N/A' %></p>
                      <p><strong>Name:</strong> <%= details.account_name || 'N/A' %></p>
                      <% if (details.routing_number) { %>
                        <p><strong>Routing:</strong> <%= details.routing_number %></p>
                      <% } %>
                    </div>
                  <% } else if (method.method_type === 'mobile_money') { %>
                    <div class="bg-gray-50 p-3 rounded text-sm">
                      <%
                        let details;
                        try {
                          details = typeof method.account_details === 'string' ?
                                   JSON.parse(method.account_details) :
                                   method.account_details;
                        } catch (e) {
                          details = {};
                        }
                      %>
                      <p><strong>Service:</strong> <%= details.service || 'N/A' %></p>
                      <p><strong>Number:</strong> <%= details.number || 'N/A' %></p>
                      <p><strong>Name:</strong> <%= details.name || 'N/A' %></p>
                    </div>
                  <% } else if (method.method_type === 'cash') { %>
                    <div class="bg-gray-50 p-3 rounded text-sm">
                      <%
                        let details;
                        try {
                          details = typeof method.account_details === 'string' ?
                                   JSON.parse(method.account_details) :
                                   method.account_details;
                        } catch (e) {
                          details = {};
                        }
                      %>
                      <p><strong>Contact:</strong> <%= details.contact_person || 'N/A' %></p>
                      <p><strong>Phone:</strong> <%= details.phone || 'N/A' %></p>
                      <p><strong>Address:</strong> <%= details.address || 'N/A' %></p>
                    </div>
                  <% } else if (method.method_type === 'credit') { %>
                    <div class="bg-gray-50 p-3 rounded text-sm">
                      <%
                        let details;
                        try {
                          details = typeof method.account_details === 'string' ?
                                   JSON.parse(method.account_details) :
                                   method.account_details;
                        } catch (e) {
                          details = {};
                        }
                      %>
                      <p><strong>Info:</strong> <%= details.description || 'Use your account credits' %></p>
                    </div>
                  <% } %>
                </div>
              </div>
            <% }) %>
          </div>
        </div>

        <!-- Transaction Reference -->
        <div>
          <label for="transactionReference" class="block text-sm font-medium text-gray-700 mb-2">
            Transaction Reference (Optional)
          </label>
          <input type="text" id="transactionReference" name="transactionReference" 
                 class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                 placeholder="Enter transaction ID or reference number">
          <p class="text-xs text-gray-500 mt-1">If you've already made the payment, enter the transaction reference</p>
        </div>

        <!-- Payment Proof -->
        <div>
          <label for="paymentProof" class="block text-sm font-medium text-gray-700 mb-2">
            Payment Proof/Notes
          </label>
          <textarea id="paymentProof" name="paymentProof" rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Describe your payment or upload receipt details..."></textarea>
        </div>

        <!-- Submit Button -->
        <div class="flex items-center justify-between">
          <a href="/fantasy" class="text-gray-600 hover:text-gray-800">← Back to Leagues</a>
          <button type="submit" id="submitPayment" 
                  class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-200 disabled:opacity-50">
            Submit Payment ($<%= league.entry_fee %>)
          </button>
        </div>
      </form>
    </div>

    <!-- Payment Instructions -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
      <h4 class="font-semibold text-yellow-800 mb-2">📋 Payment Instructions</h4>
      <ul class="text-sm text-yellow-700 space-y-1">
        <li>• Select your preferred payment method above</li>
        <li>• Make the payment of $<%= league.entry_fee %> using the provided details</li>
        <li>• Enter the transaction reference if available</li>
        <li>• Submit this form for verification</li>
        <li>• Once verified, you can start selecting players for your team</li>
      </ul>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const paymentMethodCards = document.querySelectorAll('.payment-method-card');
  const paymentForm = document.getElementById('paymentForm');
  const submitButton = document.getElementById('submitPayment');

  // Handle payment method selection
  paymentMethodCards.forEach(card => {
    card.addEventListener('click', function() {
      const radio = this.querySelector('input[type="radio"]');
      radio.checked = true;
      
      // Update UI
      paymentMethodCards.forEach(c => {
        c.classList.remove('border-blue-500', 'bg-blue-50');
        c.querySelector('.payment-details').classList.add('hidden');
      });
      
      this.classList.add('border-blue-500', 'bg-blue-50');
      this.querySelector('.payment-details').classList.remove('hidden');
    });
  });

  // Handle form submission
  paymentForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const selectedMethod = document.querySelector('input[name="paymentMethod"]:checked');
    if (!selectedMethod) {
      alert('Please select a payment method');
      return;
    }
    
    submitButton.disabled = true;
    submitButton.textContent = 'Processing...';
    
    const formData = new FormData();
    formData.append('paymentMethod', selectedMethod.value);
    formData.append('transactionReference', document.getElementById('transactionReference').value);
    formData.append('paymentProof', document.getElementById('paymentProof').value);
    
    try {
      const response = await fetch(`/fantasy/join/<%= league.id %>`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentMethod: selectedMethod.value,
          transactionReference: document.getElementById('transactionReference').value,
          paymentProof: document.getElementById('paymentProof').value
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        alert(result.message);
        window.location.href = result.redirectUrl;
      } else {
        alert(result.error || 'Payment submission failed');
      }
    } catch (error) {
      console.error('Error:', error);
      alert('An error occurred. Please try again.');
    } finally {
      submitButton.disabled = false;
      submitButton.textContent = `Submit Payment ($<%= league.entry_fee %>)`;
    }
  });
});
</script>

<style>
.payment-method-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.payment-method-card.border-blue-500 {
  background-color: #eff6ff;
}
</style>
