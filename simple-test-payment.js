const db = require('./config/database');

async function createSimpleTestPayment() {
  try {
    console.log('🔗 Creating simple test payment...');

    // Create a test payment transaction directly
    const txnRef = `TEST${Date.now()}`;
    
    const [result] = await db.query(`
      INSERT INTO payment_transactions (user_id, league_id, amount, payment_method, transaction_reference, payment_proof, payment_status)
      VALUES (1, 1, 40.00, 'bank_transfer', ?, 'Test payment for admin demo', 'pending')
    `, [txnRef]);

    console.log(`✅ Created test payment: ${txnRef}`);

    // Create participation record
    await db.query(`
      INSERT INTO user_league_participation (user_id, league_id, payment_transaction_id, participation_status, team_budget)
      VALUES (1, 1, ?, 'pending_payment', 100.00)
    `, [result.insertId]);

    console.log('✅ Created participation record');
    console.log('');
    console.log('🎉 TEST PAYMENT READY!');
    console.log('='.repeat(50));
    console.log('🔐 1. Login as admin:');
    console.log('   http://**************:3000/auth/login');
    console.log('   Username: admin');
    console.log('   Password: admin123');
    console.log('');
    console.log('🛡️ 2. Go to Admin Panel:');
    console.log('   http://**************:3000/fantasy/admin/payments');
    console.log('');
    console.log('✅ 3. Verify the payment and test email!');
    console.log('='.repeat(50));

    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

createSimpleTestPayment();
