<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
  <div class="max-w-3xl mx-auto px-4">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-gray-900 mb-2">Payment Status</h1>
      <p class="text-lg text-gray-600"><%= participation.league_name %></p>
    </div>

    <!-- Status Card -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-gray-900">Payment Information</h2>
        <div class="status-badge">
          <% if (participation.payment_status === 'verified') { %>
            <span class="bg-green-100 text-green-800 px-4 py-2 rounded-full font-semibold">
              ✅ Verified
            </span>
          <% } else if (participation.payment_status === 'pending') { %>
            <span class="bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full font-semibold">
              ⏳ Pending Verification
            </span>
          <% } else if (participation.payment_status === 'rejected') { %>
            <span class="bg-red-100 text-red-800 px-4 py-2 rounded-full font-semibold">
              ❌ Rejected
            </span>
          <% } %>
        </div>
      </div>

      <!-- Payment Details -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <h3 class="font-semibold text-gray-700 mb-3">Payment Details</h3>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-gray-600">Amount:</span>
              <span class="font-semibold">$<%= participation.entry_fee %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Method:</span>
              <span class="font-semibold capitalize"><%= participation.payment_method.replace('_', ' ') %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Reference:</span>
              <span class="font-semibold"><%= participation.transaction_reference %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Date:</span>
              <span class="font-semibold"><%= new Date(participation.payment_date).toLocaleDateString() %></span>
            </div>
          </div>
        </div>

        <div>
          <h3 class="font-semibold text-gray-700 mb-3">Team Status</h3>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-gray-600">Participation:</span>
              <span class="font-semibold capitalize"><%= participation.participation_status.replace('_', ' ') %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Team Budget:</span>
              <span class="font-semibold">$<%= participation.team_budget %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Budget Used:</span>
              <span class="font-semibold">$<%= participation.budget_used || 0 %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Players Selected:</span>
              <span class="font-semibold"><%= participation.players_selected || 0 %>/11</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Status-specific content -->
      <% if (participation.payment_status === 'verified') { %>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <h4 class="font-semibold text-green-800 mb-2">🎉 Payment Verified!</h4>
          <p class="text-green-700">Your payment has been verified. You can now start building your fantasy team!</p>
        </div>
        
        <!-- Team Building Actions -->
        <div class="flex flex-col sm:flex-row gap-4">
          <a href="/fantasy"
             class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg text-center transition-colors duration-200">
            🏆 Go to Fantasy Leagues
          </a>
          
          <a href="/fantasy" 
             class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg text-center transition-colors duration-200">
            📋 View All Leagues
          </a>
        </div>

      <% } else if (participation.payment_status === 'pending') { %>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <h4 class="font-semibold text-yellow-800 mb-2">⏳ Verification in Progress</h4>
          <p class="text-yellow-700 mb-3">Your payment is being verified by our admin team. This usually takes 1-24 hours.</p>
          <div class="text-sm text-yellow-600">
            <p>• We will verify your payment details</p>
            <p>• You'll receive an email notification once verified</p>
            <p>• Check back here for status updates</p>
          </div>
        </div>

        <div class="flex flex-col sm:flex-row gap-4">
          <button onclick="location.reload()" 
                  class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200">
            🔄 Refresh Status
          </button>
          <a href="/fantasy" 
             class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg text-center transition-colors duration-200">
            📋 Back to Leagues
          </a>
        </div>

      <% } else if (participation.payment_status === 'rejected') { %>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <h4 class="font-semibold text-red-800 mb-2">❌ Payment Rejected</h4>
          <p class="text-red-700 mb-3">Unfortunately, your payment could not be verified. Please try again with correct details.</p>
          <div class="text-sm text-red-600">
            <p>• Check your payment method and details</p>
            <p>• Ensure the payment amount matches the entry fee</p>
            <p>• Contact support if you believe this is an error</p>
          </div>
        </div>

        <div class="flex flex-col sm:flex-row gap-4">
          <a href="/fantasy/join/<%= participation.league_id %>" 
             class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg text-center transition-colors duration-200">
            💳 Try Payment Again
          </a>
          <a href="/fantasy" 
             class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg text-center transition-colors duration-200">
            📋 Back to Leagues
          </a>
        </div>
      <% } %>
    </div>

    <!-- Progress Indicator -->
    <div class="bg-white rounded-xl shadow-lg p-6">
      <h3 class="text-lg font-bold text-gray-900 mb-4">🎯 Your Progress</h3>
      
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-4">
          <!-- Step 1: Payment -->
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full flex items-center justify-center <%= participation.payment_status === 'verified' ? 'bg-green-500 text-white' : (participation.payment_status === 'pending' ? 'bg-yellow-500 text-white' : 'bg-gray-300 text-gray-600') %>">
              1
            </div>
            <span class="ml-2 text-sm font-medium">Payment</span>
          </div>
          
          <div class="w-8 h-1 bg-gray-300 <%= participation.payment_status === 'verified' ? 'bg-green-500' : '' %>"></div>
          
          <!-- Step 2: Team Creation -->
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full flex items-center justify-center <%= participation.participation_status === 'team_created' || participation.participation_status === 'active' ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600' %>">
              2
            </div>
            <span class="ml-2 text-sm font-medium">Create Team</span>
          </div>
          
          <div class="w-8 h-1 bg-gray-300 <%= participation.participation_status === 'active' ? 'bg-green-500' : '' %>"></div>
          
          <!-- Step 3: Active -->
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full flex items-center justify-center <%= participation.participation_status === 'active' ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600' %>">
              3
            </div>
            <span class="ml-2 text-sm font-medium">Active</span>
          </div>
        </div>
      </div>
      
      <div class="text-sm text-gray-600">
        <% if (participation.payment_status === 'verified') { %>
          <% if (participation.participation_status === 'paid') { %>
            ✅ Payment verified! Next: Create your fantasy team
          <% } else if (participation.participation_status === 'team_created') { %>
            ✅ Team created! Continue selecting players to activate
          <% } else if (participation.participation_status === 'active') { %>
            🎉 All set! Your team is active and ready to compete
          <% } %>
        <% } else { %>
          ⏳ Waiting for payment verification to proceed
        <% } %>
      </div>
    </div>
  </div>
</div>

<script>
// Auto-refresh for pending payments
<% if (participation.payment_status === 'pending') { %>
  setTimeout(() => {
    location.reload();
  }, 30000); // Refresh every 30 seconds
<% } %>

// Add some interactivity
document.addEventListener('DOMContentLoaded', function() {
  // Add loading states to buttons
  const buttons = document.querySelectorAll('a[href], button');
  buttons.forEach(button => {
    button.addEventListener('click', function() {
      if (this.href && !this.href.includes('javascript:')) {
        this.style.opacity = '0.7';
        this.style.pointerEvents = 'none';
      }
    });
  });
});
</script>
