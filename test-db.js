const db = require('./config/database');

async function testDatabase() {
  try {
    const [rows] = await db.query('SELECT COUNT(*) as count FROM payment_transactions WHERE payment_status = "pending"');
    console.log('Pending payments:', rows[0].count);
    
    if (rows[0].count > 0) {
      const [payments] = await db.query(`
        SELECT pt.*, u.username, fl.name as league_name
        FROM payment_transactions pt
        JOIN users u ON pt.user_id = u.id
        JOIN fantasy_leagues fl ON pt.league_id = fl.id
        WHERE pt.payment_status = 'pending'
      `);
      
      console.log('Pending payment details:', payments[0]);
      
      // Verify the payment
      await db.query(`
        UPDATE payment_transactions
        SET payment_status = 'verified', admin_notes = 'Auto-verified by script', verified_at = NOW()
        WHERE id = ?
      `, [payments[0].id]);
      
      // Update participation status
      await db.query(`
        UPDATE user_league_participation ulp
        JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
        SET ulp.participation_status = 'paid'
        WHERE pt.id = ?
      `, [payments[0].id]);
      
      console.log('✅ Payment verified successfully!');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

testDatabase();
