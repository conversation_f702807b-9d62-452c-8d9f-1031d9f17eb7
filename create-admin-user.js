const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');
require('dotenv').config();

async function createAdminUser() {
  let connection;

  try {
    // Create database connection using same config as app
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'sports_website'
    });

    console.log('🔗 Connected to database');

    // Check if admin user already exists
    const [existingAdmin] = await connection.query(
      'SELECT * FROM users WHERE username = ? OR email = ?',
      ['admin', '<EMAIL>']
    );

    if (existingAdmin.length > 0) {
      console.log('⚠️  Admin user already exists');
      console.log('Username: admin');
      console.log('Email: <EMAIL>');
      console.log('Password: admin123');
      return;
    }

    // Hash the admin password
    const hashedPassword = await bcrypt.hash('admin123', 10);

    // Create admin user
    const [result] = await connection.query(`
      INSERT INTO users (username, email, password, full_name, created_at)
      VALUES (?, ?, ?, ?, NOW())
    `, ['admin', '<EMAIL>', hashedPassword, 'System Administrator']);

    console.log('✅ Admin user created successfully!');
    console.log('');
    console.log('🔑 Admin Login Credentials:');
    console.log('Username: admin');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    console.log('');
    console.log('🌐 You can now log in and access admin features');
    console.log('Admin can verify payments at: /fantasy/admin/payments');

  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
createAdminUser();
