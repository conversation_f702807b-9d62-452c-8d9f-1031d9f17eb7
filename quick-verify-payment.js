const db = require('./config/database');

async function quickVerifyPayment() {
  try {
    // Get the most recent pending payment
    const [pendingPayments] = await db.query(`
      SELECT pt.*, u.username, u.full_name, u.email, fl.name as league_name
      FROM payment_transactions pt
      JOIN users u ON pt.user_id = u.id
      JOIN fantasy_leagues fl ON pt.league_id = fl.id
      WHERE pt.payment_status = 'pending'
      ORDER BY pt.created_at DESC
      LIMIT 1
    `);

    if (pendingPayments.length === 0) {
      console.log('✅ No pending payments found!');
      process.exit(0);
    }

    const payment = pendingPayments[0];
    console.log('📋 Found pending payment:');
    console.log(`👤 User: ${payment.full_name} (@${payment.username})`);
    console.log(`🏆 League: ${payment.league_name}`);
    console.log(`💵 Amount: $${payment.amount}`);
    console.log(`🔗 Reference: ${payment.transaction_reference}`);
    console.log('');

    // Verify the payment
    await db.query(`
      UPDATE payment_transactions
      SET payment_status = 'verified', admin_notes = 'Auto-verified by quick script', verified_at = NOW()
      WHERE id = ?
    `, [payment.id]);

    // Update participation status
    await db.query(`
      UPDATE user_league_participation ulp
      JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
      SET ulp.participation_status = 'paid'
      WHERE pt.id = ?
    `, [payment.id]);

    console.log('✅ Payment verified successfully!');
    console.log('📧 Email notification would be sent to:', payment.email);
    console.log('🎉 User can now create their fantasy team!');
    console.log('');
    console.log('🌐 Next steps:');
    console.log('1. User refreshes their browser');
    console.log('2. User goes to Fantasy Leagues page');
    console.log('3. User clicks "Create Team" button');
    console.log('4. User builds their fantasy team!');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

quickVerifyPayment();
