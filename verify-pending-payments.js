const mysql = require('mysql2/promise');
require('dotenv').config();

async function verifyPendingPayments() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'sports_website'
    });

    console.log('🔗 Connected to database');

    // Get all pending payments
    const [pendingPayments] = await connection.query(`
      SELECT pt.*, u.username, u.full_name, fl.name as league_name
      FROM payment_transactions pt
      JOIN users u ON pt.user_id = u.id
      JOIN fantasy_leagues fl ON pt.league_id = fl.id
      WHERE pt.payment_status = 'pending'
      ORDER BY pt.created_at DESC
    `);

    if (pendingPayments.length === 0) {
      console.log('✅ No pending payments found!');
      return;
    }

    console.log(`📋 Found ${pendingPayments.length} pending payment(s):`);
    console.log('');

    for (const payment of pendingPayments) {
      console.log(`💰 Payment ID: ${payment.id}`);
      console.log(`👤 User: ${payment.full_name} (@${payment.username})`);
      console.log(`🏆 League: ${payment.league_name}`);
      console.log(`💵 Amount: $${payment.amount}`);
      console.log(`📝 Method: ${payment.payment_method}`);
      console.log(`🔗 Reference: ${payment.transaction_reference}`);
      console.log(`📅 Date: ${payment.created_at}`);
      console.log('');

      // Auto-verify this payment
      await connection.query(`
        UPDATE payment_transactions
        SET payment_status = 'verified', admin_notes = 'Auto-verified by script', verified_at = NOW()
        WHERE id = ?
      `, [payment.id]);

      // Update participation status
      await connection.query(`
        UPDATE user_league_participation ulp
        JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
        SET ulp.participation_status = 'paid'
        WHERE pt.id = ?
      `, [payment.id]);

      console.log(`✅ Payment ${payment.id} verified successfully!`);
      console.log('');
    }

    console.log('🎉 All pending payments have been verified!');
    console.log('Users can now start building their fantasy teams.');

  } catch (error) {
    console.error('❌ Error verifying payments:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
verifyPendingPayments();
