const db = require('./config/database');

async function setupAdminTest() {
  try {
    console.log('🔧 Setting up admin test environment...');

    // Create a test payment for admin verification
    const txnRef = `ADMIN_TEST_${Date.now()}`;
    
    const [paymentResult] = await db.query(`
      INSERT INTO payment_transactions (user_id, league_id, amount, payment_method, transaction_reference, payment_proof, payment_status)
      VALUES (1, 1, 40.00, 'bank_transfer', ?, 'Test payment for admin verification demo', 'pending')
    `, [txnRef]);

    console.log(`✅ Created test payment: ${txnRef}`);

    // Create participation record
    await db.query(`
      INSERT INTO user_league_participation (user_id, league_id, payment_transaction_id, participation_status, team_budget)
      VALUES (1, 1, ?, 'pending_payment', 100.00)
    `, [paymentResult.insertId]);

    console.log('✅ Created participation record');

    // Check if admin user exists
    const [adminUser] = await db.query('SELECT * FROM users WHERE username = ?', ['admin']);
    
    if (adminUser.length === 0) {
      console.log('❌ Admin user not found! Creating admin user...');
      
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      await db.query(`
        INSERT INTO users (username, email, password, full_name)
        VALUES (?, ?, ?, ?)
      `, ['admin', '<EMAIL>', hashedPassword, 'System Administrator']);
      
      console.log('✅ Admin user created');
    } else {
      console.log('✅ Admin user exists');
    }

    console.log('');
    console.log('🎉 ADMIN TEST SETUP COMPLETE!');
    console.log('='.repeat(60));
    console.log('📋 STEP-BY-STEP INSTRUCTIONS:');
    console.log('='.repeat(60));
    console.log('');
    console.log('1. 🔐 LOGIN AS ADMIN:');
    console.log('   URL: http://**************:3000/auth/login');
    console.log('   Username: admin');
    console.log('   Password: admin123');
    console.log('');
    console.log('2. 🛡️ ACCESS ADMIN PANEL:');
    console.log('   Method 1: Click profile dropdown → "Admin Panel"');
    console.log('   Method 2: Direct URL: http://**************:3000/fantasy/admin/payments');
    console.log('');
    console.log('3. ✅ VERIFY PAYMENT:');
    console.log(`   - Look for payment: ${txnRef}`);
    console.log('   - Amount: $40.00');
    console.log('   - Click "Verify" button');
    console.log('   - Add notes (optional)');
    console.log('   - Confirm verification');
    console.log('');
    console.log('4. 📧 CHECK EMAIL NOTIFICATION:');
    console.log('   - Email will be logged to console');
    console.log('   - User will be notified of verification');
    console.log('');
    console.log('5. 🏆 USER CAN CREATE TEAM:');
    console.log('   - User goes to Fantasy page');
    console.log('   - Sees "Create Team" button');
    console.log('   - Can build fantasy team');
    console.log('');
    console.log('='.repeat(60));
    console.log('🚨 TROUBLESHOOTING:');
    console.log('='.repeat(60));
    console.log('If you get 404 error:');
    console.log('1. Make sure you are logged in as admin');
    console.log('2. Check browser console for errors');
    console.log('3. Try clearing browser cache/cookies');
    console.log('4. Restart the server if needed');
    console.log('='.repeat(60));

    process.exit(0);
  } catch (error) {
    console.error('❌ Error setting up admin test:', error.message);
    process.exit(1);
  }
}

setupAdminTest();
