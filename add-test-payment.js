const db = require('./config/database');

async function addTestPayment() {
  try {
    const txnRef = `TEST_ADMIN_${Date.now()}`;
    
    await db.query(`
      INSERT INTO payment_transactions (user_id, league_id, amount, payment_method, transaction_reference, payment_proof, payment_status)
      VALUES (1, 1, 40.00, 'bank_transfer', ?, 'Test payment for admin verification', 'pending')
    `, [txnRef]);

    console.log('✅ Test payment created!');
    console.log('Reference:', txnRef);
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

addTestPayment();
