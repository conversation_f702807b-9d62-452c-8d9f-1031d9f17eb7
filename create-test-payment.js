const db = require('./config/database');

async function createTestPayment() {
  try {
    console.log('🔗 Creating test payment for admin verification demo...');

    // Get a regular user (not admin)
    const [users] = await db.query(`
      SELECT * FROM users 
      WHERE username != 'admin' 
      ORDER BY created_at DESC 
      LIMIT 1
    `);

    if (users.length === 0) {
      console.log('❌ No regular users found. Creating a test user...');
      
      // Create a test user
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('password123', 10);
      
      const [userResult] = await db.query(`
        INSERT INTO users (username, email, password, full_name, created_at)
        VALUES (?, ?, ?, ?, NOW())
      `, ['testuser', '<EMAIL>', hashedPassword, 'Test User']);
      
      console.log('✅ Created test user: testuser / password123');
      var userId = userResult.insertId;
    } else {
      var userId = users[0].id;
      console.log(`✅ Using existing user: ${users[0].username} (${users[0].email})`);
    }

    // Get a fantasy league
    const [leagues] = await db.query('SELECT * FROM fantasy_leagues LIMIT 1');
    if (leagues.length === 0) {
      console.log('❌ No fantasy leagues found!');
      process.exit(1);
    }

    const league = leagues[0];
    console.log(`✅ Using league: ${league.name}`);

    // Check if user already has participation for this league
    const [existingParticipation] = await db.query(`
      SELECT * FROM user_league_participation 
      WHERE user_id = ? AND league_id = ?
    `, [userId, league.id]);

    if (existingParticipation.length > 0) {
      console.log('⚠️ User already has participation for this league. Cleaning up...');
      
      // Delete existing records
      await db.query('DELETE FROM user_league_participation WHERE user_id = ? AND league_id = ?', [userId, league.id]);
      await db.query('DELETE FROM payment_transactions WHERE user_id = ? AND league_id = ?', [userId, league.id]);
    }

    // Create a pending payment transaction
    const txnRef = `TXN${Date.now()}${Math.random().toString(36).substr(2, 5).toUpperCase()}`;
    
    const [paymentResult] = await db.query(`
      INSERT INTO payment_transactions (user_id, league_id, amount, payment_method, transaction_reference, payment_proof, payment_status)
      VALUES (?, ?, ?, ?, ?, ?, 'pending')
    `, [userId, league.id, league.entry_fee || 25.00, 'bank_transfer', txnRef, 'Test payment for admin verification']);

    console.log(`✅ Created payment transaction: ${txnRef}`);

    // Create user league participation with pending status
    await db.query(`
      INSERT INTO user_league_participation (user_id, league_id, payment_transaction_id, participation_status, team_budget)
      VALUES (?, ?, ?, 'pending_payment', 100.00)
    `, [userId, league.id, paymentResult.insertId]);

    console.log('✅ Created user league participation');

    console.log('');
    console.log('🎉 TEST PAYMENT CREATED SUCCESSFULLY!');
    console.log('='.repeat(60));
    console.log('📋 ADMIN VERIFICATION TEST INSTRUCTIONS:');
    console.log('='.repeat(60));
    console.log('1. 🔐 Login as admin:');
    console.log('   URL: http://**************:3000/auth/login');
    console.log('   Username: admin');
    console.log('   Password: admin123');
    console.log('');
    console.log('2. 🛡️ Access Admin Panel:');
    console.log('   - Click your profile dropdown in header');
    console.log('   - Click "Admin Panel"');
    console.log('   - Or go directly to: http://**************:3000/fantasy/admin/payments');
    console.log('');
    console.log('3. ✅ Verify the payment:');
    console.log(`   - You should see pending payment: ${txnRef}`);
    console.log(`   - Amount: $${league.entry_fee || 25.00}`);
    console.log(`   - League: ${league.name}`);
    console.log('   - Click "Verify" button');
    console.log('   - Add optional notes');
    console.log('');
    console.log('4. 📧 Check email notification:');
    console.log('   - Email will be sent to user');
    console.log('   - Check console for email content (if email not configured)');
    console.log('');
    console.log('5. 🏆 User can now create fantasy team:');
    console.log('   - User goes to Fantasy page');
    console.log('   - Clicks "Create Team" button');
    console.log('   - Builds their fantasy team');
    console.log('='.repeat(60));

    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating test payment:', error.message);
    process.exit(1);
  }
}

createTestPayment();
