const db = require('./config/database');

async function cleanSetupAdminTest() {
  try {
    console.log('🧹 Cleaning up existing test data...');

    // Clean up existing test data for user 1 and league 1
    await db.query('DELETE FROM user_league_participation WHERE user_id = 1 AND league_id = 1');
    await db.query('DELETE FROM payment_transactions WHERE user_id = 1 AND league_id = 1');
    
    console.log('✅ Cleaned up existing data');

    console.log('🔧 Setting up fresh admin test environment...');

    // Create a test payment for admin verification
    const txnRef = `ADMIN_TEST_${Date.now()}`;
    
    const [paymentResult] = await db.query(`
      INSERT INTO payment_transactions (user_id, league_id, amount, payment_method, transaction_reference, payment_proof, payment_status)
      VALUES (1, 1, 40.00, 'bank_transfer', ?, 'Test payment for admin verification demo', 'pending')
    `, [txnRef]);

    console.log(`✅ Created test payment: ${txnRef}`);

    // Create participation record
    await db.query(`
      INSERT INTO user_league_participation (user_id, league_id, payment_transaction_id, participation_status, team_budget)
      VALUES (1, 1, ?, 'pending_payment', 100.00)
    `, [paymentResult.insertId]);

    console.log('✅ Created participation record');

    // Check if admin user exists
    const [adminUser] = await db.query('SELECT * FROM users WHERE username = ?', ['admin']);
    
    if (adminUser.length === 0) {
      console.log('❌ Admin user not found! Creating admin user...');
      
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      await db.query(`
        INSERT INTO users (username, email, password, full_name)
        VALUES (?, ?, ?, ?)
      `, ['admin', '<EMAIL>', hashedPassword, 'System Administrator']);
      
      console.log('✅ Admin user created');
    } else {
      console.log('✅ Admin user exists');
    }

    console.log('');
    console.log('🎉 ADMIN TEST SETUP COMPLETE!');
    console.log('='.repeat(60));
    console.log('📋 COMPLETE TESTING INSTRUCTIONS:');
    console.log('='.repeat(60));
    console.log('');
    console.log('STEP 1: 🔐 LOGIN AS ADMIN');
    console.log('   1. Go to: http://**************:3000/auth/login');
    console.log('   2. Enter credentials:');
    console.log('      Username: admin');
    console.log('      Password: admin123');
    console.log('   3. Click "Login"');
    console.log('');
    console.log('STEP 2: 🛡️ ACCESS ADMIN PANEL');
    console.log('   1. After login, look for your profile dropdown (top right)');
    console.log('   2. Click on your profile dropdown');
    console.log('   3. Click "Admin Panel" (should be visible for admin users)');
    console.log('   4. OR go directly to: http://**************:3000/fantasy/admin/payments');
    console.log('');
    console.log('STEP 3: ✅ VERIFY THE TEST PAYMENT');
    console.log(`   1. You should see pending payment: ${txnRef}`);
    console.log('   2. Amount: $40.00');
    console.log('   3. Payment method: Bank Transfer');
    console.log('   4. Click the green "Verify" button');
    console.log('   5. Enter optional notes (e.g., "Test verification")');
    console.log('   6. Confirm the verification');
    console.log('');
    console.log('STEP 4: 📧 CHECK EMAIL NOTIFICATION');
    console.log('   1. Check the server console for email logs');
    console.log('   2. You should see email notification details');
    console.log('   3. Email will be sent to the user');
    console.log('');
    console.log('STEP 5: 🏆 TEST USER EXPERIENCE');
    console.log('   1. Login as regular user (or create new user)');
    console.log('   2. Go to Fantasy page');
    console.log('   3. User should see "Create Team" button');
    console.log('   4. User can now create and manage fantasy team');
    console.log('');
    console.log('='.repeat(60));
    console.log('🚨 IF YOU GET 404 ERROR:');
    console.log('='.repeat(60));
    console.log('1. ✅ Verify you are logged in as "admin"');
    console.log('2. ✅ Check browser console for JavaScript errors');
    console.log('3. ✅ Try clearing browser cache and cookies');
    console.log('4. ✅ Make sure server is running on port 3000');
    console.log('5. ✅ Try restarting the server: npm start');
    console.log('='.repeat(60));

    process.exit(0);
  } catch (error) {
    console.error('❌ Error setting up admin test:', error.message);
    process.exit(1);
  }
}

cleanSetupAdminTest();
