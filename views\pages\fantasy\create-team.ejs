<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - Online Sports</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
  <%- include('../../partials/header') %>

  <div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-gray-900 mb-4">
        <i class="fas fa-plus-circle text-blue-600 mr-3"></i>
        Create Your Fantasy Team
      </h1>
      <p class="text-xl text-gray-600">
        Join <span class="font-semibold text-blue-600"><%= league.name %></span> and start building your winning team!
      </p>
    </div>

    <!-- League Info Card -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8 max-w-2xl mx-auto">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-2xl font-bold text-gray-900">League Details</h2>
        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
          <i class="fas fa-check-circle mr-1"></i>Payment Verified
        </span>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-sm text-gray-600">League Name</div>
          <div class="font-semibold text-gray-900"><%= league.name %></div>
        </div>
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-sm text-gray-600">Entry Fee</div>
          <div class="font-semibold text-green-600">$<%= league.entry_fee %></div>
        </div>
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-sm text-gray-600">Prize Pool</div>
          <div class="font-semibold text-yellow-600">$<%= league.prize_pool || 'TBD' %></div>
        </div>
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-sm text-gray-600">Status</div>
          <div class="font-semibold text-blue-600"><%= league.status %></div>
        </div>
      </div>
    </div>

    <!-- Team Creation Form -->
    <div class="bg-white rounded-xl shadow-lg p-8 max-w-md mx-auto">
      <div class="text-center mb-6">
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-users text-blue-600 text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 mb-2">Name Your Team</h3>
        <p class="text-gray-600">Choose a unique name for your fantasy team</p>
      </div>

      <form id="createTeamForm" class="space-y-6">
        <div>
          <label for="teamName" class="block text-sm font-medium text-gray-700 mb-2">
            Team Name <span class="text-red-500">*</span>
          </label>
          <input type="text" 
                 id="teamName" 
                 name="teamName" 
                 required
                 maxlength="50"
                 placeholder="Enter your team name..."
                 class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200">
          <div id="nameError" class="hidden text-red-600 text-sm mt-2">
            <i class="fas fa-exclamation-circle mr-1"></i>
            <span id="errorMessage">Team name is required</span>
          </div>
          <div class="text-xs text-gray-500 mt-1">
            3-50 characters. Be creative and unique!
          </div>
        </div>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 class="font-semibold text-blue-800 mb-2">
            <i class="fas fa-info-circle mr-2"></i>What's Next?
          </h4>
          <ul class="text-sm text-blue-700 space-y-1">
            <li>• Create your team with a unique name</li>
            <li>• Select up to 11 players within your $100 budget</li>
            <li>• Choose your captain and vice-captain</li>
            <li>• Compete with other fantasy managers!</li>
          </ul>
        </div>

        <div class="flex items-center justify-between pt-4">
          <a href="/fantasy" 
             class="text-gray-600 hover:text-gray-800 font-medium transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>Back to Leagues
          </a>
          <button type="submit" 
                  id="createBtn"
                  class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed">
            <i class="fas fa-plus mr-2"></i>Create Team
          </button>
        </div>
      </form>
    </div>

    <!-- Tips Section -->
    <div class="max-w-2xl mx-auto mt-8">
      <div class="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-6">
        <h4 class="font-semibold text-yellow-800 mb-3">
          <i class="fas fa-lightbulb mr-2"></i>Pro Tips for Team Creation
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-yellow-700">
          <div>
            <i class="fas fa-star text-yellow-600 mr-2"></i>
            Choose a memorable team name
          </div>
          <div>
            <i class="fas fa-balance-scale text-yellow-600 mr-2"></i>
            Balance your budget wisely
          </div>
          <div>
            <i class="fas fa-chart-line text-yellow-600 mr-2"></i>
            Research player performance
          </div>
          <div>
            <i class="fas fa-trophy text-yellow-600 mr-2"></i>
            Pick strong captain choices
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.getElementById('createTeamForm');
      const teamNameInput = document.getElementById('teamName');
      const createBtn = document.getElementById('createBtn');
      const errorDiv = document.getElementById('nameError');
      const errorMessage = document.getElementById('errorMessage');

      // Real-time validation
      teamNameInput.addEventListener('input', function() {
        const value = this.value.trim();
        
        if (value.length === 0) {
          showError('Team name is required');
        } else if (value.length < 3) {
          showError('Team name must be at least 3 characters');
        } else if (value.length > 50) {
          showError('Team name must be less than 50 characters');
        } else {
          hideError();
        }
      });

      function showError(message) {
        errorMessage.textContent = message;
        errorDiv.classList.remove('hidden');
        teamNameInput.classList.add('border-red-500');
      }

      function hideError() {
        errorDiv.classList.add('hidden');
        teamNameInput.classList.remove('border-red-500');
      }

      // Form submission
      form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const teamName = teamNameInput.value.trim();
        
        if (!teamName || teamName.length < 3 || teamName.length > 50) {
          showError('Please enter a valid team name (3-50 characters)');
          return;
        }

        // Show loading state
        createBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating Team...';
        createBtn.disabled = true;

        try {
          const response = await fetch('/fantasy/teams', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              name: teamName,
              leagueId: <%= league.id %>
            })
          });

          const result = await response.json();

          if (result.success) {
            // Show success message
            createBtn.innerHTML = '<i class="fas fa-check mr-2"></i>Team Created!';
            createBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
            createBtn.classList.add('bg-green-600');
            
            // Redirect after a short delay
            setTimeout(() => {
              window.location.href = result.redirectUrl;
            }, 1000);
          } else {
            throw new Error(result.error || 'Failed to create team');
          }
        } catch (error) {
          console.error('Error creating team:', error);
          showError(error.message || 'An error occurred. Please try again.');
          
          // Reset button
          createBtn.innerHTML = '<i class="fas fa-plus mr-2"></i>Create Team';
          createBtn.disabled = false;
        }
      });
    });
  </script>
</body>
</html>
