<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - Online Sports</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
  <%- include('../../partials/header') %>

  <div class="container mx-auto px-4 py-8">
    <!-- Admin Header -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">Payment Verification</h1>
          <p class="text-gray-600">Admin Dashboard - Verify user payments for fantasy leagues</p>
        </div>
        <div class="text-right">
          <div class="text-sm text-gray-500">Logged in as</div>
          <div class="font-semibold text-blue-600"><%= user.username %></div>
        </div>
      </div>
    </div>

    <!-- Pending Payments -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
      <h2 class="text-2xl font-bold text-gray-900 mb-4">
        <i class="fas fa-clock text-yellow-500 mr-2"></i>
        Pending Payments (<%= pendingPayments.length %>)
      </h2>
      
      <% if (pendingPayments.length === 0) { %>
        <div class="text-center py-8">
          <i class="fas fa-check-circle text-green-500 text-4xl mb-4"></i>
          <p class="text-gray-600">No pending payments to verify!</p>
        </div>
      <% } else { %>
        <div class="overflow-x-auto">
          <table class="w-full table-auto">
            <thead>
              <tr class="bg-gray-50">
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">User</th>
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">League</th>
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Amount</th>
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Method</th>
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Reference</th>
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Date</th>
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody>
              <% pendingPayments.forEach(payment => { %>
                <tr class="border-b border-gray-200 hover:bg-gray-50">
                  <td class="px-4 py-3">
                    <div>
                      <div class="font-semibold text-gray-900"><%= payment.full_name %></div>
                      <div class="text-sm text-gray-600">@<%= payment.username %></div>
                      <div class="text-xs text-gray-500"><%= payment.email %></div>
                    </div>
                  </td>
                  <td class="px-4 py-3">
                    <span class="font-medium text-gray-900"><%= payment.league_name %></span>
                  </td>
                  <td class="px-4 py-3">
                    <span class="font-bold text-green-600">$<%= payment.amount %></span>
                  </td>
                  <td class="px-4 py-3">
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                      <%= payment.payment_method.replace('_', ' ').toUpperCase() %>
                    </span>
                  </td>
                  <td class="px-4 py-3">
                    <code class="text-xs bg-gray-100 px-2 py-1 rounded"><%= payment.transaction_reference %></code>
                  </td>
                  <td class="px-4 py-3">
                    <div class="text-sm text-gray-600">
                      <%= new Date(payment.created_at).toLocaleDateString() %>
                    </div>
                    <div class="text-xs text-gray-500">
                      <%= new Date(payment.created_at).toLocaleTimeString() %>
                    </div>
                  </td>
                  <td class="px-4 py-3">
                    <div class="flex space-x-2">
                      <button onclick="verifyPayment(<%= payment.id %>, 'verified')" 
                              class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors">
                        <i class="fas fa-check mr-1"></i>Verify
                      </button>
                      <button onclick="verifyPayment(<%= payment.id %>, 'rejected')" 
                              class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors">
                        <i class="fas fa-times mr-1"></i>Reject
                      </button>
                    </div>
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      <% } %>
    </div>

    <!-- Recent Payments -->
    <div class="bg-white rounded-xl shadow-lg p-6">
      <h2 class="text-2xl font-bold text-gray-900 mb-4">
        <i class="fas fa-history text-gray-500 mr-2"></i>
        Recent Verifications
      </h2>
      
      <% if (recentPayments.length === 0) { %>
        <div class="text-center py-8">
          <i class="fas fa-inbox text-gray-400 text-4xl mb-4"></i>
          <p class="text-gray-600">No recent payment verifications</p>
        </div>
      <% } else { %>
        <div class="overflow-x-auto">
          <table class="w-full table-auto">
            <thead>
              <tr class="bg-gray-50">
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">User</th>
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">League</th>
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Amount</th>
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Status</th>
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Verified By</th>
                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Date</th>
              </tr>
            </thead>
            <tbody>
              <% recentPayments.forEach(payment => { %>
                <tr class="border-b border-gray-200">
                  <td class="px-4 py-3">
                    <div>
                      <div class="font-semibold text-gray-900"><%= payment.full_name %></div>
                      <div class="text-sm text-gray-600">@<%= payment.username %></div>
                    </div>
                  </td>
                  <td class="px-4 py-3">
                    <span class="font-medium text-gray-900"><%= payment.league_name %></span>
                  </td>
                  <td class="px-4 py-3">
                    <span class="font-bold text-green-600">$<%= payment.amount %></span>
                  </td>
                  <td class="px-4 py-3">
                    <% if (payment.payment_status === 'verified') { %>
                      <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                        <i class="fas fa-check mr-1"></i>Verified
                      </span>
                    <% } else { %>
                      <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                        <i class="fas fa-times mr-1"></i>Rejected
                      </span>
                    <% } %>
                  </td>
                  <td class="px-4 py-3">
                    <span class="text-sm text-gray-600"><%= payment.verified_by_username || 'System' %></span>
                  </td>
                  <td class="px-4 py-3">
                    <div class="text-sm text-gray-600">
                      <%= new Date(payment.verified_at).toLocaleDateString() %>
                    </div>
                  </td>
                </tr>
              <% }) %>
            </tbody>
          </table>
        </div>
      <% } %>
    </div>

    <!-- Navigation -->
    <div class="mt-6 text-center">
      <a href="/fantasy" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200">
        <i class="fas fa-arrow-left mr-2"></i>Back to Fantasy Leagues
      </a>
    </div>
  </div>

  <script>
    async function verifyPayment(transactionId, status) {
      const notes = prompt(`Enter notes for this ${status} payment (optional):`);
      
      if (notes === null) return; // User cancelled
      
      try {
        const response = await fetch(`/fantasy/admin/verify-payment/${transactionId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: status,
            notes: notes || `Payment ${status} by admin`
          })
        });
        
        const result = await response.json();
        
        if (result.success) {
          alert(`Payment ${status} successfully!`);
          location.reload();
        } else {
          alert(`Error: ${result.error}`);
        }
      } catch (error) {
        console.error('Error:', error);
        alert('An error occurred while updating payment status');
      }
    }
  </script>
</body>
</html>
