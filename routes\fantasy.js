const express = require('express');
const router = express.Router();
const db = require('../config/database');
const crypto = require('crypto');

// Debug middleware to log all requests
router.use((req, res, next) => {
  console.log(`🔍 Fantasy route: ${req.method} ${req.path}`);
  console.log(`🔍 User session:`, req.session.user ? req.session.user.username : 'Not logged in');
  next();
});

// Middleware to check if user is logged in
const isAuthenticated = (req, res, next) => {
  if (req.session.user) {
    return next();
  }
  res.redirect('/auth/login?redirect=' + req.originalUrl);
};

// Fantasy leagues home page with payment integration
router.get('/', isAuthenticated, async (req, res) => {
  try {
    // Get all fantasy leagues with payment info
    const [leagues] = await db.query(`
      SELECT fl.*, sc.name as category_name, sc.color_code,
             COUNT(DISTINCT ft.id) as team_count,
             COUNT(DISTINCT ulp.id) as participants_count
      FROM fantasy_leagues fl
      LEFT JOIN sports_categories sc ON fl.category_id = sc.id
      LEFT JOIN fantasy_teams ft ON fl.id = ft.league_id
      LEFT JOIN user_league_participation ulp ON fl.id = ulp.league_id AND ulp.participation_status IN ('paid', 'team_created', 'active')
      GROUP BY fl.id
      ORDER BY fl.start_date ASC
    `);

    console.log(`📊 Found ${leagues.length} fantasy leagues in database`);
    if (leagues.length > 0) {
      console.log('Sample league:', leagues[0].name);
    }

    // Get user's league participation status
    const [userParticipation] = await db.query(`
      SELECT ulp.*, fl.name as league_name, fl.entry_fee, pt.payment_status, pt.transaction_reference
      FROM user_league_participation ulp
      JOIN fantasy_leagues fl ON ulp.league_id = fl.id
      LEFT JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
      WHERE ulp.user_id = ?
      ORDER BY ulp.joined_at DESC
    `, [req.session.user.id]);

    // Get user's fantasy teams
    const [userTeams] = await db.query(`
      SELECT ft.*, fl.name as league_name, fl.status as league_status,
             ulp.participation_status, ulp.team_budget, ulp.budget_used
      FROM fantasy_teams ft
      JOIN fantasy_leagues fl ON ft.league_id = fl.id
      JOIN user_league_participation ulp ON ft.league_id = ulp.league_id AND ft.user_id = ulp.user_id
      WHERE ft.user_id = ?
      ORDER BY ft.created_at DESC
    `, [req.session.user.id]);

    res.render('pages/fantasy/index', {
      title: 'Fantasy Sports',
      user: req.session.user,
      leagues,
      availableLeagues: leagues, // Also pass as availableLeagues for template compatibility
      userParticipation,
      userTeams
    });
  } catch (error) {
    console.error('Error loading fantasy page:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      message: 'Failed to load fantasy leagues'
    });
  }
});

// Join league - Payment step
router.get('/join/:leagueId', isAuthenticated, async (req, res) => {
  try {
    const leagueId = req.params.leagueId;

    // Get league details
    const [league] = await db.query(`
      SELECT fl.*, sc.name as category_name
      FROM fantasy_leagues fl
      LEFT JOIN sports_categories sc ON fl.category_id = sc.id
      WHERE fl.id = ?
    `, [leagueId]);

    if (league.length === 0) {
      return res.status(404).render('pages/error', {
        title: 'League Not Found',
        message: 'The requested fantasy league does not exist.'
      });
    }

    // Check if user already joined
    const [participation] = await db.query(
      'SELECT * FROM user_league_participation WHERE user_id = ? AND league_id = ?',
      [req.session.user.id, leagueId]
    );

    if (participation.length > 0) {
      return res.redirect(`/fantasy/payment-status/${leagueId}`);
    }

    // Get available payment methods
    const [paymentMethods] = await db.query(
      'SELECT * FROM payment_methods WHERE is_active = TRUE ORDER BY display_order'
    );

    res.render('pages/fantasy/join-league', {
      title: `Join ${league[0].name}`,
      league: league[0],
      paymentMethods,
      user: req.session.user
    });
  } catch (error) {
    console.error('Error loading join league page:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      message: 'Failed to load league information'
    });
  }
});

// Process payment submission
router.post('/join/:leagueId', isAuthenticated, async (req, res) => {
  try {
    const leagueId = req.params.leagueId;
    const { paymentMethod, transactionReference, paymentProof } = req.body;

    // Get league details
    const [league] = await db.query('SELECT * FROM fantasy_leagues WHERE id = ?', [leagueId]);
    if (league.length === 0) {
      return res.status(404).json({ error: 'League not found' });
    }

    // Check if user already joined
    const [existing] = await db.query(
      'SELECT * FROM user_league_participation WHERE user_id = ? AND league_id = ?',
      [req.session.user.id, leagueId]
    );

    if (existing.length > 0) {
      return res.status(400).json({ error: 'You have already joined this league' });
    }

    // Generate unique transaction reference if not provided
    const txnRef = transactionReference || `TXN${Date.now()}${Math.random().toString(36).substr(2, 5).toUpperCase()}`;

    // Create payment transaction
    const [paymentResult] = await db.query(`
      INSERT INTO payment_transactions (user_id, league_id, amount, payment_method, transaction_reference, payment_proof, payment_status)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [req.session.user.id, leagueId, league[0].entry_fee, paymentMethod, txnRef, paymentProof || 'Payment submitted', 'pending']);

    // Auto-verify for development/testing (you can disable this in production)
    const AUTO_VERIFY_PAYMENTS = process.env.NODE_ENV !== 'production'; // Set to false for production

    if (AUTO_VERIFY_PAYMENTS) {
      // Auto-verify the payment for development
      await db.query(`
        UPDATE payment_transactions
        SET payment_status = 'verified', admin_notes = 'Auto-verified for development', verified_at = NOW()
        WHERE id = ?
      `, [paymentResult.insertId]);

      // Create user league participation with paid status
      await db.query(`
        INSERT INTO user_league_participation (user_id, league_id, payment_transaction_id, participation_status, team_budget)
        VALUES (?, ?, ?, 'paid', 100.00)
      `, [req.session.user.id, leagueId, paymentResult.insertId]);
    } else {
      // Create user league participation with pending status
      await db.query(`
        INSERT INTO user_league_participation (user_id, league_id, payment_transaction_id, participation_status, team_budget)
        VALUES (?, ?, ?, 'pending_payment', 100.00)
      `, [req.session.user.id, leagueId, paymentResult.insertId]);
    }

    const message = AUTO_VERIFY_PAYMENTS
      ? 'Payment submitted and verified! You can now start building your fantasy team.'
      : 'Payment submitted successfully! Please wait for admin verification.';

    res.json({
      success: true,
      message: message,
      transactionReference: txnRef,
      redirectUrl: `/fantasy/payment-status/${leagueId}`
    });
  } catch (error) {
    console.error('Error processing payment:', error);
    res.status(500).json({ error: 'Failed to process payment' });
  }
});

// Payment status page
router.get('/payment-status/:leagueId', isAuthenticated, async (req, res) => {
  try {
    const leagueId = req.params.leagueId;

    // Get participation and payment details
    const [participation] = await db.query(`
      SELECT ulp.*, fl.name as league_name, fl.entry_fee, pt.payment_status, pt.transaction_reference, pt.payment_method, pt.created_at as payment_date
      FROM user_league_participation ulp
      JOIN fantasy_leagues fl ON ulp.league_id = fl.id
      LEFT JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
      WHERE ulp.user_id = ? AND ulp.league_id = ?
    `, [req.session.user.id, leagueId]);

    if (participation.length === 0) {
      return res.redirect(`/fantasy/join/${leagueId}`);
    }

    res.render('pages/fantasy/payment-status', {
      title: 'Payment Status',
      participation: participation[0],
      user: req.session.user
    });
  } catch (error) {
    console.error('Error loading payment status:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      message: 'Failed to load payment status'
    });
  }
});

// Team creation page
router.get('/teams/create', isAuthenticated, async (req, res) => {
  try {
    const leagueId = req.query.league;
    if (!leagueId) {
      return res.redirect('/fantasy');
    }

    // Get league details
    const [league] = await db.query('SELECT * FROM fantasy_leagues WHERE id = ?', [leagueId]);
    if (league.length === 0) {
      return res.status(404).render('pages/error', {
        title: 'League Not Found',
        message: 'The requested league could not be found.'
      });
    }

    // Check if user has paid for this league
    const [participation] = await db.query(`
      SELECT ulp.*, pt.payment_status
      FROM user_league_participation ulp
      LEFT JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
      WHERE ulp.user_id = ? AND ulp.league_id = ?
    `, [req.session.user.id, leagueId]);

    if (participation.length === 0 || participation[0].participation_status !== 'paid') {
      return res.redirect(`/fantasy/join/${leagueId}`);
    }

    // Check if user already has a team for this league
    const [existingTeam] = await db.query(
      'SELECT * FROM fantasy_teams WHERE user_id = ? AND league_id = ?',
      [req.session.user.id, leagueId]
    );

    if (existingTeam.length > 0) {
      return res.redirect(`/fantasy/teams/${existingTeam[0].id}`);
    }

    res.render('pages/fantasy/create-team', {
      title: `Create Team - ${league[0].name}`,
      league: league[0],
      user: req.session.user
    });
  } catch (error) {
    console.error('Error loading team creation page:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      message: 'Failed to load team creation page'
    });
  }
});

// Create fantasy team
router.post('/teams', isAuthenticated, async (req, res) => {
  try {
    const { name, leagueId } = req.body;

    if (!name || !leagueId) {
      return res.status(400).json({ error: 'Team name and league ID are required' });
    }

    // Validate team name
    if (name.length < 3 || name.length > 50) {
      return res.status(400).json({ error: 'Team name must be between 3 and 50 characters' });
    }

    // Get league details
    const [league] = await db.query('SELECT * FROM fantasy_leagues WHERE id = ?', [leagueId]);
    if (league.length === 0) {
      return res.status(404).json({ error: 'League not found' });
    }

    // Check if user has paid for this league
    const [participation] = await db.query(`
      SELECT ulp.*, pt.payment_status
      FROM user_league_participation ulp
      LEFT JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
      WHERE ulp.user_id = ? AND ulp.league_id = ?
    `, [req.session.user.id, leagueId]);

    if (participation.length === 0 || participation[0].participation_status !== 'paid') {
      return res.status(403).json({ error: 'Payment verification required before creating team' });
    }

    // Check if user already has a team for this league
    const [existingTeam] = await db.query(
      'SELECT * FROM fantasy_teams WHERE user_id = ? AND league_id = ?',
      [req.session.user.id, leagueId]
    );

    if (existingTeam.length > 0) {
      return res.status(400).json({ error: 'You already have a team for this league' });
    }

    // Create the fantasy team
    const [teamResult] = await db.query(`
      INSERT INTO fantasy_teams (user_id, league_id, name, created_at)
      VALUES (?, ?, ?, NOW())
    `, [req.session.user.id, leagueId, name]);

    // Update participation status to team_created
    await db.query(`
      UPDATE user_league_participation
      SET participation_status = 'team_created'
      WHERE user_id = ? AND league_id = ?
    `, [req.session.user.id, leagueId]);

    res.json({
      success: true,
      message: 'Team created successfully!',
      teamId: teamResult.insertId,
      redirectUrl: `/fantasy/teams/${teamResult.insertId}`
    });
  } catch (error) {
    console.error('Error creating fantasy team:', error);
    res.status(500).json({ error: 'Failed to create team' });
  }
});

// View single fantasy team
router.get('/teams/:id', isAuthenticated, async (req, res) => {
  try {
    // Get team details
    const [team] = await db.query(
      `SELECT ft.*, fl.name as league_name, fl.category_id, c.name as sport_name
       FROM fantasy_teams ft
       JOIN fantasy_leagues fl ON ft.league_id = fl.id
       LEFT JOIN sports_categories c ON fl.category_id = c.id
       WHERE ft.id = ?`,
      [req.params.id]
    );
    
    if (team.length === 0) {
      return res.status(404).render('pages/error', { 
        title: 'Not Found',
        message: 'Team not found' 
      });
    }
    
    // Check if user owns this team or is admin
    const isOwner = team[0].user_id === req.session.user.id;
    if (!isOwner && !req.session.user.isAdmin) {
      return res.status(403).render('pages/error', {
        title: 'Access Denied',
        message: 'You do not have permission to view this team'
      });
    }

    // Get user's participation status for payment verification
    const [participation] = await db.query(`
      SELECT ulp.*, pt.payment_status
      FROM user_league_participation ulp
      LEFT JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
      WHERE ulp.user_id = ? AND ulp.league_id = ?
    `, [req.session.user.id, team[0].league_id]);

    const canSelectPlayers = participation.length > 0 &&
                            (participation[0].participation_status === 'paid' ||
                             participation[0].participation_status === 'team_created' ||
                             participation[0].participation_status === 'active');

    // Get team players with enhanced selection data
    const [teamPlayers] = await db.query(`
      SELECT p.*, fts.id as selection_id, fts.is_captain, fts.is_vice_captain,
             fts.player_price, t.name as team_name
      FROM fantasy_team_selections fts
      JOIN players p ON fts.player_id = p.id
      JOIN teams t ON p.team_id = t.id
      WHERE fts.user_id = ? AND fts.league_id = ?
      ORDER BY fts.selection_order
    `, [req.session.user.id, team[0].league_id]);

    // Get available players for this sport (only if payment verified)
    let availablePlayers = [];
    if (canSelectPlayers) {
      const [players] = await db.query(`
        SELECT p.*, t.name as team_name
        FROM players p
        JOIN teams t ON p.team_id = t.id
        WHERE t.category_id = ? AND p.is_available = TRUE
        AND p.id NOT IN (
          SELECT player_id FROM fantasy_team_selections
          WHERE user_id = ? AND league_id = ?
        )
        ORDER BY p.price DESC, p.name
        LIMIT 100
      `, [team[0].category_id, req.session.user.id, team[0].league_id]);
      availablePlayers = players;
    }

    res.render('pages/fantasy/team', {
      title: team[0].name,
      team: team[0],
      teamPlayers,
      availablePlayers,
      participation: participation[0] || null,
      canSelectPlayers,
      isOwner,
      user: req.session.user
    });
  } catch (error) {
    console.error('Error fetching fantasy team:', error);
    res.status(500).render('pages/error', { 
      title: 'Error',
      message: 'Failed to load fantasy team' 
    });
  }
});

// Add player to fantasy team with payment verification and budget management
router.post('/teams/:id/players', isAuthenticated, async (req, res) => {
  try {
    const { playerId, position } = req.body;

    if (!playerId || !position) {
      return res.status(400).json({ error: 'Player ID and position are required' });
    }

    // Get team and league info
    const [team] = await db.query(
      'SELECT * FROM fantasy_teams WHERE id = ? AND user_id = ?',
      [req.params.id, req.session.user.id]
    );

    if (team.length === 0) {
      return res.status(403).json({ error: 'You do not have permission to modify this team' });
    }

    // Check payment status
    const [participation] = await db.query(`
      SELECT ulp.*, pt.payment_status
      FROM user_league_participation ulp
      LEFT JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
      WHERE ulp.user_id = ? AND ulp.league_id = ?
    `, [req.session.user.id, team[0].league_id]);

    if (participation.length === 0 || participation[0].participation_status === 'pending_payment') {
      return res.status(403).json({
        error: 'Payment verification required before selecting players',
        requiresPayment: true
      });
    }

    // Get player details
    const [player] = await db.query('SELECT * FROM players WHERE id = ?', [playerId]);
    if (player.length === 0) {
      return res.status(404).json({ error: 'Player not found' });
    }

    // Check budget constraints
    const newBudgetUsed = parseFloat(participation[0].budget_used) + parseFloat(player[0].price);
    if (newBudgetUsed > parseFloat(participation[0].team_budget)) {
      return res.status(400).json({
        error: `Insufficient budget. Player costs $${player[0].price}, you have $${(participation[0].team_budget - participation[0].budget_used).toFixed(2)} remaining.`
      });
    }

    // Check player limit
    if (participation[0].players_selected >= participation[0].max_players) {
      return res.status(400).json({
        error: `Team is full. Maximum ${participation[0].max_players} players allowed.`
      });
    }

    // Add player to team
    const selectionOrder = participation[0].players_selected + 1;
    await db.query(`
      INSERT INTO fantasy_team_selections (user_id, league_id, player_id, position, player_price, selection_order)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [req.session.user.id, team[0].league_id, playerId, position, player[0].price, selectionOrder]);

    // Update participation budget and player count
    await db.query(`
      UPDATE user_league_participation
      SET budget_used = ?, players_selected = ?, participation_status = 'team_created'
      WHERE user_id = ? AND league_id = ?
    `, [newBudgetUsed, selectionOrder, req.session.user.id, team[0].league_id]);

    res.status(201).json({
      success: true,
      message: `${player[0].name} added to your team!`,
      budgetRemaining: (participation[0].team_budget - newBudgetUsed).toFixed(2),
      playersSelected: selectionOrder
    });
  } catch (error) {
    console.error('Error adding player to fantasy team:', error);
    res.status(500).json({ error: 'Failed to add player' });
  }
});

// Remove player from fantasy team with budget adjustment
router.delete('/teams/:teamId/players/:selectionId', isAuthenticated, async (req, res) => {
  try {
    // Get selection details
    const [selection] = await db.query(`
      SELECT fts.*, ft.league_id
      FROM fantasy_team_selections fts
      JOIN fantasy_teams ft ON fts.league_id = ft.league_id
      WHERE fts.id = ? AND fts.user_id = ? AND ft.id = ?
    `, [req.params.selectionId, req.session.user.id, req.params.teamId]);

    if (selection.length === 0) {
      return res.status(403).json({ error: 'You do not have permission to modify this selection' });
    }

    // Get current participation
    const [participation] = await db.query(
      'SELECT * FROM user_league_participation WHERE user_id = ? AND league_id = ?',
      [req.session.user.id, selection[0].league_id]
    );

    if (participation.length === 0) {
      return res.status(404).json({ error: 'Participation record not found' });
    }

    // Remove player from team
    await db.query('DELETE FROM fantasy_team_selections WHERE id = ?', [req.params.selectionId]);

    // Update participation budget and player count
    const newBudgetUsed = parseFloat(participation[0].budget_used) - parseFloat(selection[0].player_price);
    const newPlayersSelected = participation[0].players_selected - 1;

    await db.query(`
      UPDATE user_league_participation
      SET budget_used = ?, players_selected = ?
      WHERE user_id = ? AND league_id = ?
    `, [Math.max(0, newBudgetUsed), Math.max(0, newPlayersSelected), req.session.user.id, selection[0].league_id]);

    res.json({
      success: true,
      message: 'Player removed from team',
      budgetRemaining: (participation[0].team_budget - newBudgetUsed).toFixed(2),
      playersSelected: newPlayersSelected
    });
  } catch (error) {
    console.error('Error removing player from fantasy team:', error);
    res.status(500).json({ error: 'Failed to remove player' });
  }
});

// Admin test route for debugging
router.get('/admin/test', isAuthenticated, (req, res) => {
  res.json({
    message: 'Admin route is working!',
    user: req.session.user,
    isAdmin: req.session.user ? req.session.user.username.includes('admin') : false
  });
});

// Simple admin route without authentication for testing
router.get('/admin/debug', (req, res) => {
  res.json({
    message: 'Admin debug route working!',
    session: req.session.user ? 'User logged in' : 'No user session',
    user: req.session.user || 'Not logged in'
  });
});

// Alternative admin payments route
router.get('/admin-payments', isAuthenticated, async (req, res) => {
  try {
    console.log('🔍 Alternative admin route accessed by:', req.session.user ? req.session.user.username : 'No user');

    if (!req.session.user || !req.session.user.username.includes('admin')) {
      return res.status(403).send(`
        <h1>Access Denied</h1>
        <p>Admin access required</p>
        <p>Current user: ${req.session.user ? req.session.user.username : 'Not logged in'}</p>
        <a href="/auth/login">Login</a>
      `);
    }

    // Get pending payments
    const [pendingPayments] = await db.query(`
      SELECT pt.*, u.username, u.full_name, u.email, fl.name as league_name
      FROM payment_transactions pt
      JOIN users u ON pt.user_id = u.id
      JOIN fantasy_leagues fl ON pt.league_id = fl.id
      WHERE pt.payment_status = 'pending'
      ORDER BY pt.created_at DESC
    `);

    res.send(`
      <h1>Admin Payment Verification</h1>
      <p>Welcome, ${req.session.user.username}!</p>
      <h2>Pending Payments (${pendingPayments.length})</h2>
      ${pendingPayments.map(payment => `
        <div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">
          <p><strong>User:</strong> ${payment.full_name} (${payment.username})</p>
          <p><strong>League:</strong> ${payment.league_name}</p>
          <p><strong>Amount:</strong> $${payment.amount}</p>
          <p><strong>Reference:</strong> ${payment.transaction_reference}</p>
          <button onclick="verifyPayment(${payment.id})">Verify</button>
          <button onclick="rejectPayment(${payment.id})">Reject</button>
        </div>
      `).join('')}

      <script>
        function verifyPayment(id) {
          fetch('/fantasy/admin/verify-payment/' + id, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status: 'verified', notes: 'Verified by admin' })
          }).then(r => r.json()).then(data => {
            alert(data.message);
            location.reload();
          });
        }

        function rejectPayment(id) {
          fetch('/fantasy/admin/verify-payment/' + id, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status: 'rejected', notes: 'Rejected by admin' })
          }).then(r => r.json()).then(data => {
            alert(data.message);
            location.reload();
          });
        }
      </script>
    `);
  } catch (error) {
    console.error('Error in alternative admin route:', error);
    res.status(500).send('Error: ' + error.message);
  }
});

// Admin payments dashboard
router.get('/admin/payments', isAuthenticated, async (req, res) => {
  try {
    console.log('🔍 Admin payments route accessed by:', req.session.user.username);

    // Simple admin check (you can enhance this)
    if (!req.session.user.username.includes('admin')) {
      console.log('❌ Access denied - user is not admin');
      return res.status(403).render('pages/error', {
        title: 'Access Denied',
        message: 'Admin access required. Current user: ' + req.session.user.username
      });
    }

    console.log('✅ Admin access granted');

    // Get all pending payments
    const [pendingPayments] = await db.query(`
      SELECT pt.*, u.username, u.full_name, u.email, fl.name as league_name
      FROM payment_transactions pt
      JOIN users u ON pt.user_id = u.id
      JOIN fantasy_leagues fl ON pt.league_id = fl.id
      WHERE pt.payment_status = 'pending'
      ORDER BY pt.created_at DESC
    `);

    // Get recent verified/rejected payments
    const [recentPayments] = await db.query(`
      SELECT pt.*, u.username, u.full_name, fl.name as league_name,
             admin_u.username as verified_by_username
      FROM payment_transactions pt
      JOIN users u ON pt.user_id = u.id
      JOIN fantasy_leagues fl ON pt.league_id = fl.id
      LEFT JOIN users admin_u ON pt.verified_by = admin_u.id
      WHERE pt.payment_status IN ('verified', 'rejected')
      ORDER BY pt.verified_at DESC
      LIMIT 20
    `);

    res.render('pages/fantasy/admin-payments', {
      title: 'Payment Verification - Admin',
      pendingPayments,
      recentPayments,
      user: req.session.user
    });
  } catch (error) {
    console.error('Error loading admin payments:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      message: 'Failed to load payment data'
    });
  }
});

// Admin route to verify payments
router.post('/admin/verify-payment/:transactionId', isAuthenticated, async (req, res) => {
  try {
    // Simple admin check (you can enhance this)
    if (!req.session.user.username.includes('admin')) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { status, notes } = req.body;

    // Get payment and user details for email notification
    const [paymentDetails] = await db.query(`
      SELECT pt.*, u.email, u.full_name, u.username, fl.name as league_name
      FROM payment_transactions pt
      JOIN users u ON pt.user_id = u.id
      JOIN fantasy_leagues fl ON pt.league_id = fl.id
      WHERE pt.id = ?
    `, [req.params.transactionId]);

    if (paymentDetails.length === 0) {
      return res.status(404).json({ error: 'Payment transaction not found' });
    }

    const payment = paymentDetails[0];

    // Update payment status
    await db.query(`
      UPDATE payment_transactions
      SET payment_status = ?, admin_notes = ?, verified_at = NOW(), verified_by = ?
      WHERE id = ?
    `, [status, notes, req.session.user.id, req.params.transactionId]);

    // Update participation status if payment verified
    if (status === 'verified') {
      await db.query(`
        UPDATE user_league_participation ulp
        JOIN payment_transactions pt ON ulp.payment_transaction_id = pt.id
        SET ulp.participation_status = 'paid'
        WHERE pt.id = ?
      `, [req.params.transactionId]);

      // Send email notification for verified payment
      try {
        const { sendPaymentVerificationEmail } = require('../services/emailService');
        await sendPaymentVerificationEmail(
          payment.email,
          payment.full_name,
          payment.league_name,
          payment.amount,
          payment.transaction_reference
        );
        console.log(`✅ Payment verification email sent to ${payment.email}`);
      } catch (emailError) {
        console.error('❌ Failed to send verification email:', emailError.message);
        // Don't fail the verification if email fails
      }
    }

    res.json({
      success: true,
      message: `Payment ${status} successfully${status === 'verified' ? ' and user notified by email' : ''}`
    });
  } catch (error) {
    console.error('Error verifying payment:', error);
    res.status(500).json({ error: 'Failed to verify payment' });
  }
});

module.exports = router;
